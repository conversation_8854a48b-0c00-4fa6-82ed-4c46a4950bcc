<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大秦重器 管理后台</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.1.0/dist/index.iife.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .admin-container {
            min-height: 100vh;
            background: #f5f7fa;
        }

        .header-bar {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
        }

        .logo i {
            margin-right: 10px;
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .main-content {
            padding: 20px;
            min-height: calc(100vh - 60px);
        }

        .content-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tab-content {
            padding: 20px;
        }

        .search-bar {
            margin-bottom: 20px;
        }

        .table-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .status-tag {
            font-weight: 500;
        }

        .workflow-container {
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fafafa;
        }

        .workflow-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .workflow-title {
            font-weight: 600;
            color: #303133;
        }

        .json-editor {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding: 10px;
            }
            
            .table-toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 主题变量 */
        :root {
            --bg-color: #f5f7fa;
            --card-bg: #ffffff;
            --text-primary: #303133;
            --text-regular: #606266;
            --text-secondary: #909399;
            --border-color: #dcdfe6;
            --header-bg: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            --table-header-bg: #f8f9fa;
            --table-row-hover: #f5f7fa;
            --table-stripe-bg: #fafafa;
            --input-bg: #ffffff;
            --input-border: #dcdfe6;
            --modal-bg: #ffffff;
            --modal-overlay: rgba(0, 0, 0, 0.5);
        }

        [data-theme="dark"] {
            --bg-color: #1a1a1a;
            --card-bg: #2d2d2d;
            --text-primary: #e4e7ed;
            --text-regular: #cfcfcf;
            --text-secondary: #a8abb2;
            --border-color: #4c4d4f;
            --header-bg: linear-gradient(90deg, #337ecc 0%, #529b2e 100%);
            --table-header-bg: #363636;
            --table-row-hover: #404040;
            --table-stripe-bg: #333333;
            --input-bg: #2d2d2d;
            --input-border: #4c4d4f;
            --modal-bg: #2d2d2d;
            --modal-overlay: rgba(0, 0, 0, 0.7);
        }

        body {
            background: var(--bg-color);
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .admin-container {
            background: var(--bg-color);
        }

        .header-bar {
            background: var(--header-bg);
        }

        .content-card {
            background: var(--card-bg);
            transition: background-color 0.3s ease;
        }

        /* Element Plus 组件主题适配 */
        [data-theme="dark"] .el-table {
            --el-table-bg-color: var(--card-bg);
            --el-table-tr-bg-color: var(--card-bg);
            --el-table-header-bg-color: var(--table-header-bg);
            --el-table-row-hover-bg-color: var(--table-row-hover);
            --el-table-current-row-bg-color: var(--table-row-hover);
            --el-table-stripe-bg-color: var(--table-stripe-bg);
            --el-table-border-color: var(--border-color);
            --el-table-text-color: var(--text-primary);
            --el-table-header-text-color: var(--text-primary);
        }

        [data-theme="dark"] .el-card {
            --el-card-bg-color: var(--card-bg);
            --el-card-border-color: var(--border-color);
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .el-card__header {
            background-color: var(--table-header-bg);
            border-bottom-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-card__body {
            background-color: var(--card-bg);
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-input {
            --el-input-bg-color: var(--input-bg);
            --el-input-border-color: var(--input-border);
            --el-input-text-color: var(--text-primary);
            --el-input-placeholder-color: var(--text-secondary);
        }

        [data-theme="dark"] .el-input__wrapper {
            background-color: var(--input-bg);
            border-color: var(--input-border);
        }

        [data-theme="dark"] .el-textarea {
            --el-input-bg-color: var(--input-bg);
            --el-input-border-color: var(--input-border);
            --el-input-text-color: var(--text-primary);
        }

        [data-theme="dark"] .el-textarea__inner {
            background-color: var(--input-bg);
            border-color: var(--input-border);
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-select {
            --el-select-input-color: var(--text-primary);
            --el-select-input-focus-border-color: #409eff;
        }

        [data-theme="dark"] .el-select-dropdown {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .el-select-dropdown__item {
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-select-dropdown__item:hover {
            background-color: var(--table-row-hover);
        }

        [data-theme="dark"] .el-dialog {
            --el-dialog-bg-color: var(--modal-bg);
            --el-dialog-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.24);
            background-color: var(--modal-bg);
        }

        [data-theme="dark"] .el-dialog__header {
            background-color: var(--table-header-bg);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-dialog__body {
            background-color: var(--modal-bg);
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-dialog__footer {
            background-color: var(--modal-bg);
            border-top: 1px solid var(--border-color);
        }

        [data-theme="dark"] .el-overlay {
            background-color: var(--modal-overlay);
        }

        [data-theme="dark"] .el-form-item__label {
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-tabs__header {
            background-color: var(--card-bg);
        }

        [data-theme="dark"] .el-tabs__nav-wrap::after {
            background-color: var(--border-color);
        }

        [data-theme="dark"] .el-tabs__item {
            color: var(--text-regular);
        }

        [data-theme="dark"] .el-tabs__item.is-active {
            color: #409eff;
        }

        [data-theme="dark"] .el-tabs__content {
            background-color: var(--card-bg);
        }

        [data-theme="dark"] .el-pagination {
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-pagination button {
            background-color: var(--card-bg);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .el-pagination button:hover {
            background-color: var(--table-row-hover);
        }

        [data-theme="dark"] .el-pager li {
            background-color: var(--card-bg);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .el-pager li:hover {
            background-color: var(--table-row-hover);
        }

        [data-theme="dark"] .el-pager li.is-active {
            background-color: #409eff;
            color: #ffffff;
        }

        [data-theme="dark"] .el-dropdown-menu {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .el-dropdown-menu__item {
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-dropdown-menu__item:hover {
            background-color: var(--table-row-hover);
        }

        [data-theme="dark"] .el-alert {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .el-alert__title {
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-alert__description {
            color: var(--text-regular);
        }

        /* 文件上传组件主题适配 */
        [data-theme="dark"] .el-upload {
            --el-upload-dragger-bg-color: var(--card-bg);
            --el-upload-dragger-border-color: var(--border-color);
        }

        [data-theme="dark"] .el-upload-dragger {
            background-color: var(--card-bg) !important;
            border-color: var(--border-color) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .el-upload-dragger:hover {
            border-color: #409eff !important;
        }

        [data-theme="dark"] .el-upload__text {
            color: var(--text-regular) !important;
        }

        [data-theme="dark"] .el-upload__tip {
            color: var(--text-secondary) !important;
        }

        [data-theme="dark"] .el-upload-list {
            background-color: var(--card-bg);
        }

        [data-theme="dark"] .el-upload-list__item {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-upload-list__item:hover {
            background-color: var(--table-row-hover);
        }

        /* 下拉框选项主题适配 */
        [data-theme="dark"] .el-select-dropdown__item.is-selected {
            background-color: #409eff;
            color: #ffffff;
        }

        [data-theme="dark"] .el-select-dropdown__item.is-disabled {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .el-select__input {
            color: var(--text-primary);
        }

        [data-theme="dark"] .el-select .el-input__wrapper {
            background-color: var(--input-bg);
            border-color: var(--input-border);
        }

        [data-theme="dark"] .el-select .el-input__wrapper:hover {
            border-color: #409eff;
        }

        [data-theme="dark"] .el-select .el-input__wrapper.is-focus {
            border-color: #409eff;
        }

        /* 表格间隔行颜色主题适配 */
        [data-theme="dark"] .el-table--striped .el-table__body tr.el-table__row--striped td {
            background-color: var(--table-stripe-bg);
        }

        [data-theme="dark"] .el-table__body tr:hover > td {
            background-color: var(--table-row-hover) !important;
        }

        /* 工作流容器主题适配 */
        [data-theme="dark"] .workflow-container {
            background-color: var(--table-header-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .workflow-header {
            color: var(--text-primary);
        }

        [data-theme="dark"] .workflow-title {
            color: var(--text-primary);
        }

        /* JSON编辑器主题适配 */
        [data-theme="dark"] .json-editor {
            background-color: var(--input-bg) !important;
            border-color: var(--input-border) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .json-editor:focus {
            border-color: #409eff !important;
        }

        /* 头像组件主题适配 */
        [data-theme="dark"] .el-avatar {
            background-color: #409eff;
            color: #ffffff;
        }

        /* 用户选择下拉框自定义样式 */
        .user-select-dropdown {
            max-height: 300px;
            overflow-y: auto;
        }

        .user-select-dropdown .el-select-dropdown__item {
            height: auto;
            padding: 8px 20px;
            line-height: 1.4;
        }

        .user-select-dropdown .el-select-dropdown__item:hover {
            background-color: var(--table-row-hover);
        }

        [data-theme="dark"] .user-select-dropdown {
            background-color: var(--card-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .user-select-dropdown .el-select-dropdown__item {
            color: var(--text-primary);
        }

        [data-theme="dark"] .user-select-dropdown .el-select-dropdown__item:hover {
            background-color: var(--table-row-hover);
        }

        [data-theme="dark"] .user-select-dropdown .el-select-dropdown__item.is-selected {
            background-color: #409eff;
            color: #ffffff;
        }

        /* 文件上传组件样式类 */
        .theme-upload .upload-icon {
            font-size: 67px;
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }

        .theme-upload .upload-text {
            color: var(--text-regular);
            margin-top: 10px;
            transition: color 0.3s ease;
        }

        .theme-upload .upload-tip {
            color: var(--text-secondary);
            font-size: 12px;
            margin-top: 5px;
            transition: color 0.3s ease;
        }

        [data-theme="dark"] .theme-upload .upload-icon {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .theme-upload .upload-text {
            color: var(--text-regular);
        }

        [data-theme="dark"] .theme-upload .upload-tip {
            color: var(--text-secondary);
        }

        /* 工作流容器主题样式类 */
        .theme-workflow {
            background-color: var(--table-header-bg);
            border-color: var(--border-color);
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }

        .theme-workflow .workflow-header {
            color: var(--text-primary);
        }

        .theme-workflow .workflow-title {
            color: var(--text-primary);
            font-weight: 500;
        }

        [data-theme="dark"] .theme-workflow {
            background-color: var(--table-header-bg);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .theme-workflow .workflow-header {
            color: var(--text-primary);
        }

        [data-theme="dark"] .theme-workflow .workflow-title {
            color: var(--text-primary);
        }

        /* 模型选择器主题适配 */
        .model-selector-dialog .el-table {
            --el-table-bg-color: var(--card-bg);
            --el-table-tr-bg-color: var(--card-bg);
            --el-table-header-bg-color: var(--table-header-bg);
            --el-table-row-hover-bg-color: var(--table-row-hover);
            --el-table-current-row-bg-color: var(--table-row-hover);
            --el-table-stripe-bg-color: var(--table-stripe-bg);
            --el-table-border-color: var(--border-color);
            --el-table-text-color: var(--text-primary);
            --el-table-header-text-color: var(--text-primary);
        }

        [data-theme="dark"] .model-selector-dialog .el-table {
            background-color: var(--card-bg);
        }

        [data-theme="dark"] .model-selector-dialog .el-table__body tr:hover > td {
            background-color: var(--table-row-hover) !important;
        }

        [data-theme="dark"] .model-selector-dialog .el-table__body tr.current-row > td {
            background-color: var(--table-row-hover) !important;
        }

        /* 动画效果 */
        .fade-enter-active,
        .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from,
        .fade-leave-to {
            opacity: 0;
        }

        .slide-enter-active,
        .slide-leave-active {
            transition: all 0.3s ease;
        }

        .slide-enter-from {
            transform: translateX(-100%);
        }

        .slide-leave-to {
            transform: translateX(100%);
        }

        /* 主题切换动画 */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="admin-container">
            <!-- 顶部导航栏 -->
            <div class="header-bar">
                <div class="header-content">
                    <div class="logo">
                        <i class="el-icon-setting"></i>
                        大秦重器 管理后台
                    </div>
                    <div class="user-info">
                        <el-button type="primary" link @click="openApiDocs">
                            <i class="el-icon-document"></i>
                            API 文档
                        </el-button>
                        <el-button type="primary" link @click="toggleTheme">
                            <i :class="isDarkTheme ? 'el-icon-sunny' : 'el-icon-moon'"></i>
                            {{ isDarkTheme ? '浅色' : '深色' }}
                        </el-button>
                        <el-button type="primary" link @click="openSettings">
                            <i class="el-icon-setting"></i>
                            设置
                        </el-button>
                        <el-dropdown @command="handleUserCommand">
                            <span class="el-dropdown-link">
                                <i class="el-icon-user"></i>
                                管理员
                                <i class="el-icon-arrow-down"></i>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="logout">
                                        <i class="el-icon-switch-button"></i>
                                        退出登录
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <div class="content-card">
                    <!-- 标签页导航 -->
                    <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
                        <el-tab-pane label="用户管理" name="users">
                            <user-management ref="userManagement"></user-management>
                        </el-tab-pane>
                        <el-tab-pane label="文件管理" name="files">
                            <file-management ref="fileManagement"></file-management>
                        </el-tab-pane>
                        <el-tab-pane label="项目管理" name="projects">
                            <project-management ref="projectManagement"></project-management>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
        </div>

        <!-- 设置对话框 -->
        <el-dialog
            v-model="settingsDialog.visible"
            title="系统设置"
            width="600px"
            @close="resetSettingsForm"
        >
            <el-form
                ref="settingsFormRef"
                :model="settingsDialog.form"
                :rules="settingsDialog.rules"
                label-width="120px"
            >
                <el-card style="margin-bottom: 20px;">
                    <template #header>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>OpenAI API 配置</span>
                            <el-button
                                type="primary"
                                size="small"
                                @click="testOpenAIConnection"
                                :loading="settingsDialog.testing"
                            >
                                测试连接
                            </el-button>
                        </div>
                    </template>

                    <el-form-item label="API Base URL" prop="openaiBaseUrl">
                        <el-input
                            v-model="settingsDialog.form.openaiBaseUrl"
                            placeholder="例如: https://api.openai.com/v1"
                            clearable
                        >
                            <template #prepend>
                                <el-icon><Link /></el-icon>
                            </template>
                        </el-input>
                        <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                            OpenAI API 的基础URL地址，支持自定义代理
                        </div>
                    </el-form-item>

                    <el-form-item label="API Key" prop="openaiApiKey">
                        <el-input
                            v-model="settingsDialog.form.openaiApiKey"
                            type="password"
                            placeholder="请输入您的 OpenAI API Key"
                            show-password
                            clearable
                        >
                            <template #prepend>
                                <el-icon><Key /></el-icon>
                            </template>
                        </el-input>
                        <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                            您的 OpenAI API 密钥，将安全保存在本地浏览器中
                        </div>
                    </el-form-item>

                    <el-form-item label="默认模型" prop="openaiModel">
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <el-input
                                v-model="settingsDialog.form.openaiModel"
                                placeholder="选择或输入模型名称"
                                style="flex: 1;"
                                readonly
                                @click="openModelSelector"
                            >
                                <template #suffix>
                                    <el-icon style="cursor: pointer;" @click="openModelSelector">
                                        <Search />
                                    </el-icon>
                                </template>
                            </el-input>
                            <el-button
                                type="primary"
                                :icon="List"
                                @click="openModelSelector"
                                :loading="settingsDialog.loadingModels"
                            >
                                选择模型
                            </el-button>
                        </div>
                        <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                            点击"选择模型"按钮打开模型选择器，支持搜索和筛选
                        </div>
                    </el-form-item>

                    <!-- 连接状态显示 -->
                    <el-alert
                        v-if="settingsDialog.connectionStatus"
                        :title="settingsDialog.connectionStatus.message"
                        :type="settingsDialog.connectionStatus.success ? 'success' : 'error'"
                        style="margin-top: 10px;"
                        show-icon
                        :closable="false"
                    />
                </el-card>

                <el-card header="界面设置">
                    <el-form-item label="主题模式">
                        <el-radio-group v-model="settingsDialog.form.theme">
                            <el-radio label="light">浅色主题</el-radio>
                            <el-radio label="dark">深色主题</el-radio>
                            <el-radio label="auto">跟随系统</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="语言设置">
                        <el-select
                            v-model="settingsDialog.form.language"
                            placeholder="选择语言"
                            style="width: 200px"
                        >
                            <el-option label="简体中文" value="zh-CN" />
                            <el-option label="English" value="en-US" />
                        </el-select>
                    </el-form-item>
                </el-card>
            </el-form>

            <template #footer>
                <el-button @click="settingsDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="saveSettings" :loading="settingsDialog.saving">
                    保存设置
                </el-button>
            </template>
        </el-dialog>

        <!-- 模型选择器对话框 -->
        <el-dialog
            v-model="modelSelectorDialog.visible"
            title="选择AI模型"
            width="800px"
            @close="resetModelSelector"
            :close-on-click-modal="false"
            class="model-selector-dialog"
        >
            <div style="margin-bottom: 20px;">
                <el-row :gutter="16" style="margin-bottom: 16px;">
                    <el-col :span="18">
                        <el-input
                            v-model="modelSelectorDialog.searchQuery"
                            placeholder="搜索模型名称..."
                            clearable
                            @input="handleModelSearch"
                        >
                            <template #prefix>
                                <el-icon><Search /></el-icon>
                            </template>
                        </el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-button
                            type="primary"
                            :icon="Refresh"
                            @click="refreshModelList"
                            :loading="modelSelectorDialog.loading"
                            style="width: 100%;"
                        >
                            刷新模型列表
                        </el-button>
                    </el-col>
                </el-row>

                <!-- 提供商标签页 -->
                <el-tabs
                    v-model="modelSelectorDialog.activeTab"
                    @tab-change="handleModelTabChange"
                    type="card"
                >
                    <el-tab-pane label="所有模型" name="all">
                        <template #label>
                            <span>
                                <el-icon><Grid /></el-icon>
                                所有模型 ({{ getTotalModelCount() }})
                            </span>
                        </template>
                    </el-tab-pane>
                    <el-tab-pane
                        v-for="owner in modelSelectorDialog.owners"
                        :key="owner"
                        :label="owner"
                        :name="owner"
                    >
                        <template #label>
                            <span>
                                <el-tag :type="getOwnerTagType(owner)" size="small" style="margin-right: 5px;">
                                    {{ owner }}
                                </el-tag>
                                ({{ getModelCountByOwner(owner) }})
                            </span>
                        </template>
                    </el-tab-pane>
                </el-tabs>
            </div>

            <el-table
                :data="filteredModels"
                v-loading="modelSelectorDialog.loading"
                element-loading-text="加载模型中..."
                style="width: 100%"
                height="400"
                stripe
                border
                @row-click="selectModel"
                highlight-current-row
            >
                <el-table-column prop="id" label="模型名称" min-width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div style="display: flex; align-items: center;">
                            <el-icon style="margin-right: 8px; color: #409eff;"><Cpu /></el-icon>
                            <div>
                                <div style="font-weight: 500;">{{ row.id }}</div>
                                <div v-if="row.description" style="font-size: 12px; color: #909399;">
                                    {{ row.description }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column prop="owned_by" label="提供商" width="120">
                    <template #default="{ row }">
                        <el-tag :type="getOwnerTagType(row.owned_by || row.owner || row.provider)" size="small">
                            {{ row.owned_by || row.owner || row.provider || '未知' }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="created" label="创建时间" width="180">
                    <template #default="{ row }">
                        <span v-if="row.created">
                            {{ formatModelDate(row.created) }}
                        </span>
                        <span v-else style="color: #909399;">-</span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="100" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            type="primary"
                            size="small"
                            @click.stop="selectModel(row)"
                        >
                            选择
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div style="margin-top: 20px; text-align: center;">
                <el-pagination
                    v-model:current-page="modelSelectorDialog.pagination.page"
                    v-model:page-size="modelSelectorDialog.pagination.limit"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="modelSelectorDialog.pagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleModelPageSizeChange"
                    @current-change="handleModelPageChange"
                />
            </div>

            <template #footer>
                <el-button @click="modelSelectorDialog.visible = false">取消</el-button>
                <el-button
                    type="primary"
                    @click="confirmModelSelection"
                    :disabled="!modelSelectorDialog.selectedModel"
                >
                    确认选择
                </el-button>
            </template>
        </el-dialog>

        <!-- 全局加载遮罩 -->
        <el-loading
            v-loading="globalLoading"
            element-loading-text="加载中..."
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :fullscreen="true">
        </el-loading>
    </div>

    <!-- 引入组件脚本 -->
    <script src="/admin/js/utils/api.js"></script>
    <script src="/admin/js/utils/auth.js"></script>
    <script src="/admin/js/utils/theme.js"></script>
    <script src="/admin/js/utils/settings.js"></script>
    <script src="/admin/js/components/UserManagement.js"></script>
    <script src="/admin/js/components/FileManagement.js"></script>
    <script src="/admin/js/components/ProjectManagement.js"></script>
    <script src="/admin/js/app.js"></script>
</body>
</html>
